export default class MainMenu extends Phaser.Scene {
    constructor() {
        super("MainMenu");
    }

    create() {
        // Create a cozy sky gradient background
        const graphics = this.add.graphics();
        graphics.fillGradientStyle(0x87CEEB, 0x87CEEB, 0xFFE4E1, 0xFFE4E1, 1);
        graphics.fillRect(0, 0, 1280, 720);

        // Add floating clouds for atmosphere
        this.createClouds();

        // Game title with bouncy animation
        const title = this.add.text(640, 200, "🌸 <PERSON>lufftail's Garden 🌸", {
            fontSize: '64px',
            fontFamily: 'Arial',
            fill: '#4A5D23',
            stroke: '#FFFFFF',
            strokeThickness: 4,
            shadow: {
                offsetX: 3,
                offsetY: 3,
                color: '#000000',
                blur: 5,
                fill: true
            }
        });
        title.setOrigin(0.5);

        // Subtitle
        const subtitle = this.add.text(640, 280, "Help Fluff<PERSON> rebuild her magical garden!", {
            fontSize: '24px',
            fontFamily: 'Arial',
            fill: '#2F4F2F',
            align: 'center'
        });
        subtitle.setOrigin(0.5);

        // Create Flufftail character preview
        this.createFlufftailPreview();

        // Start button
        const startButton = this.add.graphics();
        startButton.fillStyle(0x90EE90);
        startButton.fillRoundedRect(-100, -30, 200, 60, 20);
        startButton.lineStyle(4, 0x228B22);
        startButton.strokeRoundedRect(-100, -30, 200, 60, 20);
        startButton.x = 640;
        startButton.y = 500;

        const startText = this.add.text(640, 500, "🌱 Start Adventure 🌱", {
            fontSize: '28px',
            fontFamily: 'Arial',
            fill: '#2F4F2F'
        });
        startText.setOrigin(0.5);

        // Make button interactive
        const buttonZone = this.add.zone(640, 500, 200, 60);
        buttonZone.setInteractive();
        buttonZone.on('pointerdown', () => {
            this.scene.start("GameLevel");
        });

        buttonZone.on('pointerover', () => {
            startButton.setScale(1.1);
            startText.setScale(1.1);
        });

        buttonZone.on('pointerout', () => {
            startButton.setScale(1);
            startText.setScale(1);
        });

        // Add bouncy animation to title
        this.tweens.add({
            targets: title,
            scaleX: 1.05,
            scaleY: 1.05,
            duration: 2000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });

        // Instructions text
        const instructions = this.add.text(640, 600, "Use arrow keys to move, SPACE to jump and interact", {
            fontSize: '18px',
            fontFamily: 'Arial',
            fill: '#4A5D23',
            align: 'center'
        });
        instructions.setOrigin(0.5);
    }

    createClouds() {
        // Create some floating clouds
        for (let i = 0; i < 5; i++) {
            const cloud = this.add.graphics();
            cloud.fillStyle(0xFFFFFF, 0.8);
            
            // Draw cloud shape
            cloud.fillCircle(0, 0, 30);
            cloud.fillCircle(-25, 0, 25);
            cloud.fillCircle(25, 0, 25);
            cloud.fillCircle(-15, -15, 20);
            cloud.fillCircle(15, -15, 20);
            
            cloud.x = Phaser.Math.Between(100, 1180);
            cloud.y = Phaser.Math.Between(50, 200);
            
            // Gentle floating animation
            this.tweens.add({
                targets: cloud,
                x: cloud.x + Phaser.Math.Between(-50, 50),
                y: cloud.y + Phaser.Math.Between(-20, 20),
                duration: Phaser.Math.Between(3000, 5000),
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });
        }
    }

    createFlufftailPreview() {
        // Create a simple bunny character using graphics
        const bunny = this.add.graphics();
        
        // Body (oval)
        bunny.fillStyle(0xF5DEB3); // Wheat color
        bunny.fillEllipse(0, 0, 60, 80);
        
        // Head (circle)
        bunny.fillCircle(0, -50, 35);
        
        // Ears
        bunny.fillEllipse(-15, -75, 12, 30);
        bunny.fillEllipse(15, -75, 12, 30);
        
        // Inner ears
        bunny.fillStyle(0xFFB6C1); // Light pink
        bunny.fillEllipse(-15, -75, 6, 15);
        bunny.fillEllipse(15, -75, 6, 15);
        
        // Eyes
        bunny.fillStyle(0x000000);
        bunny.fillCircle(-10, -55, 4);
        bunny.fillCircle(10, -55, 4);
        
        // Eye shine
        bunny.fillStyle(0xFFFFFF);
        bunny.fillCircle(-8, -57, 2);
        bunny.fillCircle(12, -57, 2);
        
        // Nose
        bunny.fillStyle(0xFFB6C1);
        bunny.fillTriangle(0, -45, -3, -40, 3, -40);
        
        // Tail
        bunny.fillStyle(0xFFFFFF);
        bunny.fillCircle(25, 10, 12);
        
        bunny.x = 400;
        bunny.y = 400;
        
        // Bouncy animation for Flufftail
        this.tweens.add({
            targets: bunny,
            y: bunny.y - 10,
            duration: 1500,
            yoyo: true,
            repeat: -1,
            ease: 'Bounce.easeInOut'
        });
    }
}
