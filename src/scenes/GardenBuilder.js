export default class GardenBuilder extends Phaser.Scene {
    constructor() {
        super("GardenBuilder");
        this.placedItems = [];
        this.draggedItem = null;
    }

    init(data) {
        this.gameData = data || { seeds: 0, blocks: 0, decorations: 0 };
    }

    create() {
        // Create garden background
        const graphics = this.add.graphics();
        graphics.fillGradientStyle(0x87CEEB, 0x87CEEB, 0x90EE90, 0x90EE90, 1);
        graphics.fillRect(0, 0, 1280, 720);

        // Create garden ground
        const ground = this.add.graphics();
        ground.fillStyle(0x8FBC8F);
        ground.fillRoundedRect(50, 400, 1180, 270, 20);

        // Add some decorative elements
        this.createDecorations();

        // Create UI
        this.createUI();

        // Create item palette
        this.createItemPalette();

        // Set up input
        this.input.on('dragstart', this.onDragStart, this);
        this.input.on('drag', this.onDrag, this);
        this.input.on('dragend', this.onDragEnd, this);
    }

    createDecorations() {
        // Add some clouds
        for (let i = 0; i < 3; i++) {
            const cloud = this.add.graphics();
            cloud.fillStyle(0xFFFFFF, 0.8);
            
            // Draw cloud shape
            cloud.fillCircle(0, 0, 25);
            cloud.fillCircle(-20, 0, 20);
            cloud.fillCircle(20, 0, 20);
            cloud.fillCircle(-12, -12, 15);
            cloud.fillCircle(12, -12, 15);
            
            cloud.x = Phaser.Math.Between(200, 1080);
            cloud.y = Phaser.Math.Between(50, 150);
            
            // Gentle floating animation
            this.tweens.add({
                targets: cloud,
                x: cloud.x + Phaser.Math.Between(-30, 30),
                duration: Phaser.Math.Between(4000, 6000),
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });
        }

        // Add garden border flowers
        for (let i = 0; i < 10; i++) {
            const flower = this.add.graphics();
            flower.fillStyle(0xFF69B4); // Hot pink
            flower.fillCircle(0, 0, 5);
            flower.fillStyle(0xFFB6C1); // Light pink
            flower.fillCircle(0, 0, 3);
            flower.x = 70 + i * 115;
            flower.y = 410;
        }
    }

    createUI() {
        // Title
        const title = this.add.text(640, 50, "🌸 Flufftail's Garden Builder 🌸", {
            fontSize: '36px',
            fontFamily: 'Arial',
            fill: '#2F4F2F',
            stroke: '#FFFFFF',
            strokeThickness: 2
        });
        title.setOrigin(0.5);

        // Instructions
        const instructions = this.add.text(640, 100, "Drag and drop items from your collection to build your garden!", {
            fontSize: '18px',
            fontFamily: 'Arial',
            fill: '#4A5D23',
            align: 'center'
        });
        instructions.setOrigin(0.5);

        // Back to level button
        const backButton = this.add.graphics();
        backButton.fillStyle(0xFFB6C1);
        backButton.fillRoundedRect(0, 0, 150, 40, 10);
        backButton.x = 50;
        backButton.y = 50;

        const backText = this.add.text(125, 70, '← Back to Level', {
            fontSize: '16px',
            fontFamily: 'Arial',
            fill: '#2F4F2F'
        });
        backText.setOrigin(0.5);

        // Make back button interactive
        const backZone = this.add.zone(125, 70, 150, 40);
        backZone.setInteractive();
        backZone.on('pointerdown', () => {
            this.scene.start("GameLevel");
        });

        // Collection display
        this.createCollectionDisplay();
    }

    createCollectionDisplay() {
        // Collection background
        const collectionBg = this.add.graphics();
        collectionBg.fillStyle(0x000000, 0.3);
        collectionBg.fillRoundedRect(50, 150, 300, 200, 10);

        // Collection title
        const collectionTitle = this.add.text(200, 170, "Your Collection", {
            fontSize: '20px',
            fontFamily: 'Arial',
            fill: '#FFFFFF'
        });
        collectionTitle.setOrigin(0.5);

        // Display collected items
        this.seedCountText = this.add.text(70, 200, `🌱 Seeds: ${this.gameData.seeds}`, {
            fontSize: '16px',
            fontFamily: 'Arial',
            fill: '#FFFFFF'
        });

        this.blockCountText = this.add.text(70, 225, `🧱 Blocks: ${this.gameData.blocks}`, {
            fontSize: '16px',
            fontFamily: 'Arial',
            fill: '#FFFFFF'
        });

        this.decorationCountText = this.add.text(70, 250, `🦋 Decorations: ${this.gameData.decorations}`, {
            fontSize: '16px',
            fontFamily: 'Arial',
            fill: '#FFFFFF'
        });
    }

    createItemPalette() {
        // Item palette background
        const paletteBg = this.add.graphics();
        paletteBg.fillStyle(0x000000, 0.2);
        paletteBg.fillRoundedRect(400, 150, 830, 200, 10);

        const paletteTitle = this.add.text(815, 170, "Drag Items to Your Garden", {
            fontSize: '20px',
            fontFamily: 'Arial',
            fill: '#2F4F2F'
        });
        paletteTitle.setOrigin(0.5);

        // Create draggable items based on collection
        let xOffset = 450;
        
        // Seeds
        for (let i = 0; i < this.gameData.seeds; i++) {
            this.createDraggableItem('seed', xOffset, 220);
            xOffset += 60;
        }

        // Blocks
        for (let i = 0; i < this.gameData.blocks; i++) {
            this.createDraggableItem('block', xOffset, 220);
            xOffset += 60;
        }

        // Decorations
        for (let i = 0; i < this.gameData.decorations; i++) {
            this.createDraggableItem('decoration', xOffset, 220);
            xOffset += 60;
        }
    }

    createDraggableItem(type, x, y) {
        const item = this.add.graphics();
        
        switch (type) {
            case 'seed':
                item.fillStyle(0x8B4513); // Saddle brown
                item.fillCircle(0, 0, 15);
                item.fillStyle(0x90EE90); // Light green
                item.fillCircle(0, -5, 8);
                break;
            case 'block':
                item.fillStyle(0x8B4513); // Brown
                item.fillRoundedRect(-15, -15, 30, 30, 5);
                item.lineStyle(2, 0x654321);
                item.strokeRoundedRect(-15, -15, 30, 30, 5);
                break;
            case 'decoration':
                // Butterfly decoration
                item.fillStyle(0xFF69B4); // Hot pink
                item.fillEllipse(-8, -5, 12, 8);
                item.fillEllipse(8, -5, 12, 8);
                item.fillStyle(0x000000);
                item.fillCircle(0, 0, 2);
                break;
        }
        
        item.x = x;
        item.y = y;
        item.itemType = type;
        item.setInteractive();
        this.input.setDraggable(item);
        
        return item;
    }

    onDragStart(pointer, gameObject) {
        this.draggedItem = gameObject;
        gameObject.setScale(1.2);
        gameObject.setAlpha(0.8);
    }

    onDrag(pointer, gameObject, dragX, dragY) {
        gameObject.x = dragX;
        gameObject.y = dragY;
    }

    onDragEnd(pointer, gameObject) {
        gameObject.setScale(1);
        gameObject.setAlpha(1);
        
        // Check if dropped in garden area
        if (gameObject.y > 400 && gameObject.y < 670 && 
            gameObject.x > 50 && gameObject.x < 1230) {
            
            // Place item in garden
            this.placeItemInGarden(gameObject);
        } else {
            // Return to original position
            this.tweens.add({
                targets: gameObject,
                x: gameObject.originalX || gameObject.x,
                y: gameObject.originalY || gameObject.y,
                duration: 300,
                ease: 'Back.easeOut'
            });
        }
        
        this.draggedItem = null;
    }

    placeItemInGarden(item) {
        // Create a permanent copy in the garden
        const placedItem = this.createGardenItem(item.itemType, item.x, item.y);
        this.placedItems.push(placedItem);
        
        // Remove the draggable item
        item.destroy();
        
        // Add some sparkle effect
        this.createSparkleEffect(placedItem.x, placedItem.y);
    }

    createGardenItem(type, x, y) {
        const item = this.add.graphics();
        
        switch (type) {
            case 'seed':
                // Grown plant
                item.fillStyle(0x228B22); // Forest green
                item.fillRect(-2, -20, 4, 20);
                item.fillStyle(0x90EE90); // Light green
                item.fillEllipse(-8, -25, 12, 8);
                item.fillEllipse(8, -25, 12, 8);
                item.fillStyle(0xFF69B4); // Pink flower
                item.fillCircle(0, -30, 5);
                break;
            case 'block':
                item.fillStyle(0x8B4513); // Brown
                item.fillRoundedRect(-15, -15, 30, 30, 5);
                item.lineStyle(2, 0x654321);
                item.strokeRoundedRect(-15, -15, 30, 30, 5);
                break;
            case 'decoration':
                // Animated butterfly
                item.fillStyle(0xFF69B4); // Hot pink
                item.fillEllipse(-8, -5, 12, 8);
                item.fillEllipse(8, -5, 12, 8);
                item.fillStyle(0x000000);
                item.fillCircle(0, 0, 2);
                
                // Add gentle animation
                this.tweens.add({
                    targets: item,
                    y: item.y - 10,
                    duration: 2000,
                    yoyo: true,
                    repeat: -1,
                    ease: 'Sine.easeInOut'
                });
                break;
        }
        
        item.x = x;
        item.y = y;
        return item;
    }

    createSparkleEffect(x, y) {
        for (let i = 0; i < 5; i++) {
            const sparkle = this.add.graphics();
            sparkle.fillStyle(0xFFD700); // Gold
            sparkle.fillStar(0, 0, 4, 3, 6);
            sparkle.x = x + Phaser.Math.Between(-20, 20);
            sparkle.y = y + Phaser.Math.Between(-20, 20);
            
            this.tweens.add({
                targets: sparkle,
                scaleX: 0,
                scaleY: 0,
                alpha: 0,
                duration: 1000,
                ease: 'Power2',
                onComplete: () => sparkle.destroy()
            });
        }
    }
}
