export default class GameLevel extends Phaser.Scene {
  constructor() {
    super("GameLevel");
    this.gameData = {
      seeds: 0,
      blocks: 0,
      decorations: 0,
    };
  }

  create() {
    // Create sky gradient background
    const graphics = this.add.graphics();
    graphics.fillGradientStyle(0x87ceeb, 0x87ceeb, 0x98fb98, 0x98fb98, 1);
    graphics.fillRect(0, 0, 1280, 720);

    // Create floating island platforms
    this.createPlatforms();

    // Create Flufftail character
    this.createFlufftail();

    // Create collectibles
    this.createCollectibles();

    // Create gentle challenges
    this.createChallenges();

    // Create UI
    this.createUI();

    // Set up controls
    this.cursors = this.input.keyboard.createCursorKeys();
    this.spaceKey = this.input.keyboard.addKey(
      Phaser.Input.Keyboard.KeyCodes.SPACE
    );
    this.helperKey = this.input.keyboard.addKey(
      Phaser.Input.Keyboard.KeyCodes.H
    );

    // Initialize helper system
    this.currentHelper = null;
    this.helperCooldown = false;

    // Camera follows player
    this.cameras.main.startFollow(this.flufftail);
    this.cameras.main.setBounds(0, 0, 1280, 720);
  }

  createFlufftail() {
    // Create Flufftail as a physics sprite
    this.flufftail = this.add.graphics();
    this.physics.add.existing(this.flufftail);

    // Draw Flufftail
    this.drawFlufftail();

    // Set physics properties
    this.flufftail.body.setSize(40, 60);
    this.flufftail.body.setCollideWorldBounds(true);
    this.flufftail.body.setBounce(0.2);

    // Position Flufftail
    this.flufftail.x = 100;
    this.flufftail.y = 500;

    // Animation properties
    this.flufftail.isJumping = false;
    this.flufftail.facingRight = true;
  }

  drawFlufftail() {
    this.flufftail.clear();

    const scaleX = this.flufftail.facingRight ? 1 : -1;

    // Body (oval)
    this.flufftail.fillStyle(0xf5deb3); // Wheat color
    this.flufftail.fillEllipse(0, 0, 40, 60);

    // Head (circle)
    this.flufftail.fillCircle(0, -35, 25);

    // Ears
    this.flufftail.fillEllipse(-10 * scaleX, -55, 8, 20);
    this.flufftail.fillEllipse(10 * scaleX, -55, 8, 20);

    // Inner ears
    this.flufftail.fillStyle(0xffb6c1); // Light pink
    this.flufftail.fillEllipse(-10 * scaleX, -55, 4, 10);
    this.flufftail.fillEllipse(10 * scaleX, -55, 4, 10);

    // Eyes
    this.flufftail.fillStyle(0x000000);
    this.flufftail.fillCircle(-7 * scaleX, -40, 3);
    this.flufftail.fillCircle(7 * scaleX, -40, 3);

    // Eye shine
    this.flufftail.fillStyle(0xffffff);
    this.flufftail.fillCircle(-6 * scaleX, -42, 1);
    this.flufftail.fillCircle(8 * scaleX, -42, 1);

    // Nose
    this.flufftail.fillStyle(0xffb6c1);
    this.flufftail.fillTriangle(0, -32, -2, -28, 2, -28);

    // Tail
    this.flufftail.fillStyle(0xffffff);
    this.flufftail.fillCircle(15 * scaleX, 5, 8);
  }

  createPlatforms() {
    this.platforms = this.physics.add.staticGroup();

    // Ground platform
    const ground = this.add.graphics();
    ground.fillStyle(0x8fbc8f); // Dark sea green
    ground.fillRoundedRect(0, 0, 1280, 100, 20);
    ground.y = 620;
    this.physics.add.existing(ground, true);
    this.platforms.add(ground);

    // Floating platforms
    const platformData = [
      { x: 300, y: 500, width: 150, height: 30 },
      { x: 600, y: 400, width: 120, height: 30 },
      { x: 900, y: 350, width: 140, height: 30 },
      { x: 1100, y: 250, width: 100, height: 30 },
    ];

    platformData.forEach((data) => {
      const platform = this.add.graphics();
      platform.fillStyle(0x9acd32); // Yellow green
      platform.fillRoundedRect(0, 0, data.width, data.height, 10);
      platform.x = data.x;
      platform.y = data.y;
      this.physics.add.existing(platform, true);
      this.platforms.add(platform);
    });
  }

  createCollectibles() {
    this.seeds = this.physics.add.group();
    this.blocks = this.physics.add.group();
    this.decorations = this.physics.add.group();

    // Create seeds
    const seedPositions = [
      { x: 350, y: 450 },
      { x: 650, y: 350 },
      { x: 950, y: 300 },
      { x: 1150, y: 200 },
    ];

    seedPositions.forEach((pos) => {
      const seed = this.add.graphics();
      seed.fillStyle(0x8b4513); // Saddle brown
      seed.fillCircle(0, 0, 8);
      seed.fillStyle(0x90ee90); // Light green
      seed.fillCircle(0, -3, 4);
      seed.x = pos.x;
      seed.y = pos.y;
      this.physics.add.existing(seed);
      this.seeds.add(seed);

      // Gentle floating animation
      this.tweens.add({
        targets: seed,
        y: seed.y - 10,
        duration: 2000,
        yoyo: true,
        repeat: -1,
        ease: "Sine.easeInOut",
      });
    });

    // Create blocks
    const blockPositions = [
      { x: 200, y: 580 },
      { x: 500, y: 460 },
      { x: 800, y: 410 },
    ];

    blockPositions.forEach((pos) => {
      const block = this.add.graphics();
      block.fillStyle(0x8b4513); // Brown
      block.fillRoundedRect(-10, -10, 20, 20, 3);
      block.lineStyle(2, 0x654321);
      block.strokeRoundedRect(-10, -10, 20, 20, 3);
      block.x = pos.x;
      block.y = pos.y;
      this.physics.add.existing(block);
      this.blocks.add(block);

      // Gentle rotation
      this.tweens.add({
        targets: block,
        rotation: 0.1,
        duration: 3000,
        yoyo: true,
        repeat: -1,
        ease: "Sine.easeInOut",
      });
    });

    // Create decorations (butterflies)
    const decorationPositions = [
      { x: 400, y: 300 },
      { x: 700, y: 250 },
      { x: 1000, y: 200 },
    ];

    decorationPositions.forEach((pos) => {
      const decoration = this.add.graphics();
      decoration.fillStyle(0xff69b4); // Hot pink
      decoration.fillEllipse(-6, -3, 8, 6);
      decoration.fillEllipse(6, -3, 8, 6);
      decoration.fillStyle(0x000000);
      decoration.fillCircle(0, 0, 1);
      decoration.x = pos.x;
      decoration.y = pos.y;
      this.physics.add.existing(decoration);
      this.decorations.add(decoration);

      // Flutter animation
      this.tweens.add({
        targets: decoration,
        x: decoration.x + Phaser.Math.Between(-30, 30),
        y: decoration.y + Phaser.Math.Between(-20, 20),
        duration: 2500,
        yoyo: true,
        repeat: -1,
        ease: "Sine.easeInOut",
      });
    });
  }

  createChallenges() {
    // Create a simple color matching challenge
    this.colorFlowers = this.physics.add.group();
    this.colorTargets = this.physics.add.staticGroup();

    const colors = [
      { color: 0xff69b4, name: "pink" },
      { color: 0x90ee90, name: "green" },
      { color: 0x87ceeb, name: "blue" },
    ];

    // Create colored flowers to collect
    colors.forEach((colorData, index) => {
      const flower = this.add.graphics();
      flower.fillStyle(colorData.color);
      flower.fillCircle(0, 0, 12);
      flower.fillStyle(0xffff00); // Yellow center
      flower.fillCircle(0, 0, 5);
      flower.x = 250 + index * 200;
      flower.y = 580;
      flower.colorName = colorData.name;
      this.physics.add.existing(flower);
      this.colorFlowers.add(flower);

      // Gentle glow animation
      this.tweens.add({
        targets: flower,
        scaleX: 1.2,
        scaleY: 1.2,
        duration: 1500,
        yoyo: true,
        repeat: -1,
        ease: "Sine.easeInOut",
      });
    });

    // Create matching targets on platforms
    colors.forEach((colorData, index) => {
      const target = this.add.graphics();
      target.fillStyle(0x000000, 0.3);
      target.fillCircle(0, 0, 20);
      target.lineStyle(3, colorData.color);
      target.strokeCircle(0, 0, 20);
      target.x = 350 + index * 300;
      target.y = 470;
      target.colorName = colorData.name;
      target.matched = false;
      this.physics.add.existing(target, true);
      this.colorTargets.add(target);

      // Add instruction text
      const instruction = this.add.text(
        target.x,
        target.y - 40,
        `Place ${colorData.name} flower here`,
        {
          fontSize: "12px",
          fontFamily: "Arial",
          fill: "#FFFFFF",
          backgroundColor: "#000000",
          padding: { x: 5, y: 3 },
        }
      );
      instruction.setOrigin(0.5);
    });

    // Create a friendly animal that needs feeding
    this.createFriendlyAnimal();
  }

  createFriendlyAnimal() {
    // Create a cute rabbit friend
    const rabbit = this.add.graphics();
    rabbit.fillStyle(0xdeb887); // Burlywood
    rabbit.fillEllipse(0, 0, 30, 40);
    rabbit.fillCircle(0, -25, 20);

    // Ears
    rabbit.fillEllipse(-8, -35, 6, 15);
    rabbit.fillEllipse(8, -35, 6, 15);

    // Eyes
    rabbit.fillStyle(0x000000);
    rabbit.fillCircle(-6, -28, 2);
    rabbit.fillCircle(6, -28, 2);

    // Nose
    rabbit.fillStyle(0xffb6c1);
    rabbit.fillTriangle(0, -22, -2, -18, 2, -18);

    rabbit.x = 1000;
    rabbit.y = 580;

    // Add speech bubble
    const speechBubble = this.add.graphics();
    speechBubble.fillStyle(0xffffff);
    speechBubble.fillRoundedRect(-40, -30, 80, 25, 5);
    speechBubble.fillTriangle(-5, -5, 0, 5, 5, -5);
    speechBubble.x = rabbit.x;
    speechBubble.y = rabbit.y - 60;

    const speechText = this.add.text(rabbit.x, rabbit.y - 47, "I'm hungry!", {
      fontSize: "12px",
      fontFamily: "Arial",
      fill: "#000000",
    });
    speechText.setOrigin(0.5);

    // Bouncy animation
    this.tweens.add({
      targets: rabbit,
      y: rabbit.y - 5,
      duration: 1000,
      yoyo: true,
      repeat: -1,
      ease: "Sine.easeInOut",
    });

    this.friendlyAnimal = rabbit;
    this.animalSpeech = { bubble: speechBubble, text: speechText };
  }

  createUI() {
    // UI background
    const uiBackground = this.add.graphics();
    uiBackground.fillStyle(0x000000, 0.3);
    uiBackground.fillRoundedRect(10, 10, 300, 80, 10);
    uiBackground.setScrollFactor(0);

    // Collectible counters
    this.seedText = this.add.text(20, 20, "🌱 Seeds: 0", {
      fontSize: "20px",
      fontFamily: "Arial",
      fill: "#FFFFFF",
    });
    this.seedText.setScrollFactor(0);

    this.blockText = this.add.text(20, 45, "🧱 Blocks: 0", {
      fontSize: "20px",
      fontFamily: "Arial",
      fill: "#FFFFFF",
    });
    this.blockText.setScrollFactor(0);

    this.decorationText = this.add.text(20, 70, "🦋 Decorations: 0", {
      fontSize: "20px",
      fontFamily: "Arial",
      fill: "#FFFFFF",
    });
    this.decorationText.setScrollFactor(0);

    // Garden button
    const gardenButton = this.add.graphics();
    gardenButton.fillStyle(0x90ee90);
    gardenButton.fillRoundedRect(0, 0, 120, 40, 10);
    gardenButton.x = 1140;
    gardenButton.y = 20;
    gardenButton.setScrollFactor(0);

    const gardenText = this.add.text(1200, 40, "🏡 Garden", {
      fontSize: "16px",
      fontFamily: "Arial",
      fill: "#2F4F2F",
    });
    gardenText.setOrigin(0.5);
    gardenText.setScrollFactor(0);

    // Make garden button interactive
    const gardenZone = this.add.zone(1200, 40, 120, 40);
    gardenZone.setInteractive();
    gardenZone.setScrollFactor(0);
    gardenZone.on("pointerdown", () => {
      this.scene.start("GardenBuilder", this.gameData);
    });

    // Helper instructions
    const helperText = this.add.text(
      20,
      100,
      "Press H to summon magical helpers!",
      {
        fontSize: "16px",
        fontFamily: "Arial",
        fill: "#FFFFFF",
      }
    );
    helperText.setScrollFactor(0);
  }

  update() {
    // Handle movement
    if (this.cursors.left.isDown) {
      this.flufftail.body.velocity.x = -200;
      if (this.flufftail.facingRight) {
        this.flufftail.facingRight = false;
        this.drawFlufftail();
      }
    } else if (this.cursors.right.isDown) {
      this.flufftail.body.velocity.x = 200;
      if (!this.flufftail.facingRight) {
        this.flufftail.facingRight = true;
        this.drawFlufftail();
      }
    } else {
      this.flufftail.body.velocity.x = 0;
    }

    // Handle jumping
    if (this.spaceKey.isDown && this.flufftail.body.touching.down) {
      this.flufftail.body.velocity.y = -500;
      this.flufftail.isJumping = true;
    }

    if (this.flufftail.body.touching.down) {
      this.flufftail.isJumping = false;
    }

    // Handle helper summoning
    if (this.helperKey.isDown && !this.helperCooldown && !this.currentHelper) {
      this.summonHelper();
    }

    // Set up collisions (only once)
    if (!this.collisionsSet) {
      this.physics.add.collider(this.flufftail, this.platforms);
      this.physics.add.overlap(
        this.flufftail,
        this.seeds,
        this.collectSeed,
        null,
        this
      );
      this.physics.add.overlap(
        this.flufftail,
        this.blocks,
        this.collectBlock,
        null,
        this
      );
      this.physics.add.overlap(
        this.flufftail,
        this.decorations,
        this.collectDecoration,
        null,
        this
      );
      this.physics.add.overlap(
        this.flufftail,
        this.colorFlowers,
        this.collectColorFlower,
        null,
        this
      );
      this.collisionsSet = true;
    }
  }

  collectSeed(flufftail, seed) {
    seed.destroy();
    this.gameData.seeds++;
    this.seedText.setText(`🌱 Seeds: ${this.gameData.seeds}`);

    // Play collection effect
    const effect = this.add.text(seed.x, seed.y - 20, "+1 Seed!", {
      fontSize: "16px",
      fontFamily: "Arial",
      fill: "#90EE90",
    });
    effect.setOrigin(0.5);

    this.tweens.add({
      targets: effect,
      y: effect.y - 30,
      alpha: 0,
      duration: 1000,
      onComplete: () => effect.destroy(),
    });
  }

  collectBlock(flufftail, block) {
    block.destroy();
    this.gameData.blocks++;
    this.blockText.setText(`🧱 Blocks: ${this.gameData.blocks}`);

    // Play collection effect
    const effect = this.add.text(block.x, block.y - 20, "+1 Block!", {
      fontSize: "16px",
      fontFamily: "Arial",
      fill: "#8B4513",
    });
    effect.setOrigin(0.5);

    this.tweens.add({
      targets: effect,
      y: effect.y - 30,
      alpha: 0,
      duration: 1000,
      onComplete: () => effect.destroy(),
    });
  }

  collectDecoration(flufftail, decoration) {
    decoration.destroy();
    this.gameData.decorations++;
    this.decorationText.setText(`🦋 Decorations: ${this.gameData.decorations}`);

    // Play collection effect
    const effect = this.add.text(
      decoration.x,
      decoration.y - 20,
      "+1 Butterfly!",
      {
        fontSize: "16px",
        fontFamily: "Arial",
        fill: "#FF69B4",
      }
    );
    effect.setOrigin(0.5);

    this.tweens.add({
      targets: effect,
      y: effect.y - 30,
      alpha: 0,
      duration: 1000,
      onComplete: () => effect.destroy(),
    });
  }

  collectColorFlower(flufftail, flower) {
    // Store the flower color for matching
    this.carriedFlower = flower.colorName;
    flower.destroy();

    // Show feedback
    const effect = this.add.text(
      flower.x,
      flower.y - 20,
      `Picked up ${flower.colorName} flower!`,
      {
        fontSize: "14px",
        fontFamily: "Arial",
        fill: "#FFD700",
      }
    );
    effect.setOrigin(0.5);

    this.tweens.add({
      targets: effect,
      y: effect.y - 30,
      alpha: 0,
      duration: 1500,
      onComplete: () => effect.destroy(),
    });

    // Check if player is near a matching target
    this.colorTargets.children.entries.forEach((target) => {
      const distance = Phaser.Math.Distance.Between(
        this.flufftail.x,
        this.flufftail.y,
        target.x,
        target.y
      );

      if (
        distance < 50 &&
        target.colorName === this.carriedFlower &&
        !target.matched
      ) {
        this.matchColorFlower(target);
      }
    });
  }

  matchColorFlower(target) {
    target.matched = true;
    this.carriedFlower = null;

    // Visual feedback
    const successEffect = this.add.text(
      target.x,
      target.y - 30,
      "Perfect Match! ✨",
      {
        fontSize: "16px",
        fontFamily: "Arial",
        fill: "#90EE90",
      }
    );
    successEffect.setOrigin(0.5);

    // Fill the target
    target.clear();
    target.fillStyle(
      target.colorName === "pink"
        ? 0xff69b4
        : target.colorName === "green"
        ? 0x90ee90
        : 0x87ceeb
    );
    target.fillCircle(0, 0, 20);

    // Sparkle effect
    for (let i = 0; i < 8; i++) {
      const sparkle = this.add.graphics();
      sparkle.fillStyle(0xffd700);
      sparkle.fillStar(0, 0, 4, 3, 6);
      sparkle.x = target.x + Phaser.Math.Between(-30, 30);
      sparkle.y = target.y + Phaser.Math.Between(-30, 30);

      this.tweens.add({
        targets: sparkle,
        scaleX: 0,
        scaleY: 0,
        alpha: 0,
        duration: 1000,
        ease: "Power2",
        onComplete: () => sparkle.destroy(),
      });
    }

    this.tweens.add({
      targets: successEffect,
      y: successEffect.y - 40,
      alpha: 0,
      duration: 2000,
      onComplete: () => successEffect.destroy(),
    });

    // Check if all flowers are matched
    const allMatched = this.colorTargets.children.entries.every(
      (t) => t.matched
    );
    if (allMatched) {
      this.completeChallengeReward();
    }
  }

  completeChallengeReward() {
    // Give bonus rewards
    this.gameData.seeds += 2;
    this.gameData.decorations += 1;
    this.seedText.setText(`🌱 Seeds: ${this.gameData.seeds}`);
    this.decorationText.setText(`🦋 Decorations: ${this.gameData.decorations}`);

    // Celebration message
    const celebration = this.add.text(
      640,
      300,
      "🎉 Challenge Complete! 🎉\nBonus rewards earned!",
      {
        fontSize: "24px",
        fontFamily: "Arial",
        fill: "#FFD700",
        align: "center",
        stroke: "#000000",
        strokeThickness: 2,
      }
    );
    celebration.setOrigin(0.5);
    celebration.setScrollFactor(0);

    this.tweens.add({
      targets: celebration,
      scaleX: 1.2,
      scaleY: 1.2,
      duration: 500,
      yoyo: true,
      repeat: 1,
      onComplete: () => {
        this.time.delayedCall(2000, () => celebration.destroy());
      },
    });
  }

  summonHelper() {
    // Randomly choose a helper
    const helpers = ["puffy", "blinky"];
    const helperType = Phaser.Utils.Array.GetRandom(helpers);

    if (helperType === "puffy") {
      this.summonPuffy();
    } else if (helperType === "blinky") {
      this.summonBlinky();
    }

    // Set cooldown
    this.helperCooldown = true;
    this.time.delayedCall(5000, () => {
      this.helperCooldown = false;
    });
  }

  summonPuffy() {
    // Create Puffy the Cloud
    const puffy = this.add.graphics();
    puffy.fillStyle(0xffffff, 0.9);
    puffy.fillCircle(0, 0, 40);
    puffy.fillCircle(-30, 0, 30);
    puffy.fillCircle(30, 0, 30);
    puffy.fillCircle(-20, -20, 25);
    puffy.fillCircle(20, -20, 25);

    puffy.x = this.flufftail.x;
    puffy.y = this.flufftail.y - 100;
    this.currentHelper = puffy;

    // Add helpful text
    const helpText = this.add.text(
      puffy.x,
      puffy.y - 60,
      "Puffy helps you jump higher!",
      {
        fontSize: "16px",
        fontFamily: "Arial",
        fill: "#4169E1",
      }
    );
    helpText.setOrigin(0.5);

    // Puffy gives double jump power for 10 seconds
    this.flufftail.doubleJumpPower = true;

    this.time.delayedCall(10000, () => {
      puffy.destroy();
      helpText.destroy();
      this.currentHelper = null;
      this.flufftail.doubleJumpPower = false;
    });

    // Gentle floating animation
    this.tweens.add({
      targets: puffy,
      y: puffy.y - 20,
      duration: 2000,
      yoyo: true,
      repeat: 4,
      ease: "Sine.easeInOut",
    });
  }

  summonBlinky() {
    // Create Blinky the Firefly
    const blinky = this.add.graphics();
    blinky.fillStyle(0xffff00); // Yellow
    blinky.fillCircle(0, 0, 8);
    blinky.fillStyle(0x000000);
    blinky.fillCircle(-3, -2, 2);
    blinky.fillCircle(3, -2, 2);

    blinky.x = this.flufftail.x + 50;
    blinky.y = this.flufftail.y - 30;
    this.currentHelper = blinky;

    // Add helpful text
    const helpText = this.add.text(
      blinky.x,
      blinky.y - 30,
      "Blinky lights the way!",
      {
        fontSize: "16px",
        fontFamily: "Arial",
        fill: "#FFD700",
      }
    );
    helpText.setOrigin(0.5);

    // Create light effect
    const light = this.add.graphics();
    light.fillStyle(0xffff00, 0.3);
    light.fillCircle(0, 0, 100);
    light.x = blinky.x;
    light.y = blinky.y;

    this.time.delayedCall(10000, () => {
      blinky.destroy();
      helpText.destroy();
      light.destroy();
      this.currentHelper = null;
    });

    // Blinking animation
    this.tweens.add({
      targets: [blinky, light],
      alpha: 0.3,
      duration: 500,
      yoyo: true,
      repeat: 19,
      ease: "Sine.easeInOut",
    });

    // Follow Flufftail
    this.tweens.add({
      targets: [blinky, light, helpText],
      x: this.flufftail.x + 50,
      duration: 1000,
      repeat: 9,
      ease: "Power2",
    });
  }
}
