import Preload from "./scenes/Preload.js";
import MainMenu from "./scenes/MainMenu.js";
import GameLevel from "./scenes/GameLevel.js";
import GardenBuilder from "./scenes/GardenBuilder.js";

window.addEventListener("load", function () {
  var game = new Phaser.Game({
    width: 1280,
    height: 720,
    type: Phaser.AUTO,
    backgroundColor: "#87CEEB", // Sky blue background for cozy feel
    scale: {
      mode: Phaser.Scale.FIT,
      autoCenter: Phaser.Scale.CENTER_BOTH,
    },
    physics: {
      default: "arcade",
      arcade: {
        gravity: { y: 800 },
        debug: false,
      },
    },
  });

  game.scene.add("Preload", Preload);
  game.scene.add("MainMenu", MainMenu);
  game.scene.add("GameLevel", GameLevel);
  game.scene.add("GardenBuilder", GardenBuilder);
  game.scene.add("Boot", Boot, true);
});

class Boot extends Phaser.Scene {
  preload() {
    this.load.pack("pack", "assets/preload-asset-pack.json");
  }

  create() {
    this.scene.start("Preload");
  }
}
