---
description: 
globs: 
alwaysApply: true
---
## Scenes

- The most common task of a user in Phaser Editor is creating scenes with the Scene Editor.
- The Scene Editor is a powerful WYSIWYG tool and we encourage always to use it first instead of writing coding. It is what a Phaser Editor uses expects: getting immediate visual feedback by designing visual scenes.
- The Scene Editor files are JSON files with the `.scene` extension. It describes a hierarchy of game objects and their properties.
- `.scene` files should be edited only by the Scene Editor. Never write directly into a `.scene` file.
- If you want to modify a scene, you should do it by calling the Phaser Editor MCP tools.
- When you add a new scene using the Editor MCP tools, it is only added to the project, but not registered into the game instance. You have to add the scene to the game instance in the code if you want it to be shown in the game.

## Scene compiler

- The Scene Editor compiles `.scene` files into JavaScript or TypeScript files (JavaScript by default).
- So, basically, Phaser Editor is a visual tool but also it is a code compiler. It gets the `.scene` files and compiles human readable code files.
- The code file has the same name of the scene file, but just changing the extension. For example, a `Level.scene` file is compiled to a `Level.js`.
- This is very important: the code generated by the Scene Editor can be modified by the user. Actually, that's the idea, that the user can edit the code files generated by the editor, using third-party coding tools like VS Code. However, the user should only modify certain zones in the code. When the Scene Editor generates code, it gets the user code written in the protected zones, and merge it back with the new generated code.
- The protected zones where the user can write its code are delimitted by comments like:
    - /* START-USER-IMPORTS */ .... /* END-USER-IMPORTS */
    - /* START-USER-CTR-CODE */ ... /* END-USER-CTR-CODE */
    - /* START-USER-CODE */ ... /* END-USER-CODE */
- You should be smart enough to modify only the parts of the code that is dedicated to user code. Never, never, modify parts of the code that are not inside the user zones. This is critical! If you write outside the user zones, that code will be lost the next time the code is generated.
- The code generated by Phaser Editor uses the Phaser API and is just like any other Phaser code. It is standard Phaser coding so you can use all your expertise in Phaser.
- Look we provide the Phaser Editor MCP tools to modify the scenes. Please, always use first these tools if possible. Don't create static objects via code when you can easily create them in the Scene Editor by using the Phaser Editor MCP tools. The user would like to edit and preview the scenes in the Scene Editor. It is what they expect!
- This is very important: if you modify a scene via the Phaser Editor MCP tools, then you need to say the editor to save it so it will also generate the code. In this way you can get always the `.scene` files in sync with the code.

## Accessing game objects generated by the Scene Editor

- The game objects are mostly designed in the Scene Efditor, and the logic of the game is implemented by modifying the generated code. So, in many cases you will need to set the scope of the game objects to public or class, in this way, the scene compiler will generate variable fields to reference the obejcts, so you can access the objects from any method of the scene.
- To change the sope of game objects defined in the Scene Editor you have to use the Phaser Editor MCP tools.
- Don't use weird code to access a game object that was created in the Scene Editor. All that you have to do is to change the scope of the game object using the Phaser Editor MCP tools.