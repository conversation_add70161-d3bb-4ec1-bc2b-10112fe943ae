# 🌸 <PERSON><PERSON><PERSON><PERSON>'s Garden 🌸

A cozy, kid-friendly platformer game built with Phaser.js featuring <PERSON><PERSON><PERSON><PERSON>, a magical bunny who helps rebuild her floating garden paradise!

## 🎮 Game Features

### Story
You play as <PERSON><PERSON><PERSON><PERSON>, a magical bunny who lives in a floating garden in the sky. One day, all her animal friends' homes got scrambled by a mischievous wind spirit! Help <PERSON><PERSON><PERSON><PERSON> rebuild their homes, solve fun mini-challenges, and grow her garden paradise back to life.

### Gameplay
- **Explore Floating Islands**: Navigate through cozy 2D platforming levels
- **Collect Items**: Gather seeds 🌱, building blocks 🧱, and decorations 🦋
- **Garden Builder**: Use drag-and-drop to place collected items in your garden
- **Magical Helpers**: Press H to summon <PERSON><PERSON>y the Cloud or Blinky the Firefly
- **Gentle Challenges**: Complete color-matching puzzles for bonus rewards
- **No Fail States**: Kid-friendly design with no time limits or game overs

### Controls
- **Arrow Keys**: Move Flufftail left and right
- **Spacebar**: Jump and interact
- **H Key**: Summon magical helpers
- **Mouse**: Drag and drop items in the garden builder

### Game Scenes
1. **Main Menu**: Welcome screen with bouncy animations
2. **Game Level**: Platforming adventure with collectibles and challenges
3. **Garden Builder**: Creative mode to design your garden

## 🚀 How to Run

### Option 1: Local Server
```bash
python3 -m http.server 8000
```
Then open http://localhost:8000

### Option 2: Phaser Editor v5
Open the project in Phaser Editor v5 and run the project.

## 🎨 Visual Style
- Hand-drawn, paper-cutout aesthetic
- Bright, cozy color palette with soft outlines
- Bouncy, squishy character animations
- Everything feels alive and friendly

## 🌟 Special Features
- **Puffy the Cloud**: Gives Flufftail enhanced jumping abilities
- **Blinky the Firefly**: Provides magical lighting effects
- **Color Matching**: Match colored flowers to their targets for rewards
- **Progressive Garden Growth**: Your garden expands as you collect more items
- **Gentle Animations**: Everything moves with smooth, calming motions

## 🎯 Game Mechanics

### Collectibles
- **Seeds 🌱**: Grow into beautiful plants in your garden
- **Blocks 🧱**: Build structures and platforms
- **Decorations 🦋**: Add magical butterflies and ornaments

### Challenges
- **Color Matching**: Pick up colored flowers and place them on matching targets
- **Animal Friends**: Meet friendly creatures who need your help
- **Magical Helpers**: Use special abilities to overcome obstacles

### Garden Building
- Drag and drop collected items anywhere in your garden
- Watch your garden come to life with animations
- No wrong choices - everything looks beautiful!

## 🐰 Meet Flufftail
Flufftail is a magical bunny with:
- Bouncy movement and adorable animations
- The ability to summon helpful magical creatures
- A kind heart that wants to help all her friends
- Unlimited energy for garden building adventures

Enjoy helping Flufftail rebuild her magical garden! 🌺✨
